# Team Deployment Guide - Secure Telegram Bot on AWS (FREE TIER)

## Overview

Deploy a production-ready, secure Telegram bot on AWS EC2 with:
- $0 AWS costs (12-month free tier)
- Maximum security (encrypted, firewall, restricted access)
- Auto-restart on failures
- Team-friendly setup process
- Complete monitoring with free logging

---

## Prerequisites for Team Members

### 1. AWS Account Setup
- [ ] Create AWS account (free tier eligible)
- [ ] Verify email and phone number
- [ ] Set up billing alerts (optional but recommended)

### 2. Required Information
- [ ] **Telegram API credentials** (from https://my.telegram.org)
- [ ] **Bot token** (from @BotFather)
- [ ] **Admin user ID** (your Telegram user ID)
- [ ] **Admin chat ID** (group where you want admin buttons)
- [ ] **Google Sheets name** (for lead tracking)
- [ ] **Google Service Account** credentials

### 3. Local Tools
- [ ] AWS CLI installed
- [ ] SSH client (Terminal/PuTTY)
- [ ] Text editor

---

## Security Features

### Network Security
- **Firewall**: UFW enabled with minimal rules
- **SSH**: Restricted to your IP only
- **Outbound**: Only HTTPS to Telegram API
- **No open ports** except SSH from your IP

### Application Security
- **Non-root execution**: Bot runs as `ubuntu` user
- **File permissions**: Strict 600/700 permissions on sensitive files
- **Process isolation**: Systemd security features enabled
- **Encrypted storage**: EBS volumes encrypted at rest
- **Secure logging**: No sensitive data in logs

### Access Control
- **Admin-only functions**: Restricted to your user/chat IDs
- **SSH key authentication**: No password login
- **Sudo restrictions**: Limited sudo access for bot user

---

## Cost Optimization (FREE TIER)

### Free Tier Limits
- **EC2**: 750 hours/month t2.micro (enough for 24/7)
- **EBS**: 30GB storage (we use 8GB)
- **Data Transfer**: 15GB outbound (plenty for bot traffic)
- **CloudWatch**: 10 custom metrics, 5GB logs

### Cost Monitoring
```bash
# Set up billing alerts (recommended)
aws budgets create-budget --account-id YOUR_ACCOUNT_ID \
  --budget file://budget-alert.json
```

---

## Quick Start for Team Members

### Step 1: Get Your Credentials Ready

#### Telegram API Credentials
1. Go to https://my.telegram.org
2. Log in with your phone number
3. Go to "API Development Tools"
4. Create new application
5. Note down **API ID** and **API Hash**

#### Bot Token
1. Message @BotFather on Telegram
2. Create new bot: `/newbot`
3. Follow instructions
4. Note down **Bot Token**

#### Your User ID
1. Message @userinfobot on Telegram
2. Note down your **User ID**

#### Admin Chat ID
1. Add your bot to the admin group
2. Send a message in the group
3. Visit: `https://api.telegram.org/botYOUR_BOT_TOKEN/getUpdates`
4. Find the chat ID (negative number for groups)

#### Google Sheets Setup
1. Go to Google Cloud Console
2. Create new project
3. Enable Google Sheets API
4. Create Service Account
5. Download credentials.json
6. Share your Google Sheet with the service account email

### Step 2: Deploy Infrastructure

#### Launch EC2 Instance
```bash
# Use the provided CloudFormation template
aws cloudformation create-stack \
  --stack-name telegram-bot-YOURNAME \
  --template-body file://secure-bot-infrastructure.yaml \
  --parameters ParameterKey=KeyPairName,ParameterValue=YOUR_KEY_PAIR \
               ParameterKey=AllowedSSHIP,ParameterValue=YOUR_IP/32 \
               ParameterKey=TeamMemberName,ParameterValue=YOURNAME \
  --capabilities CAPABILITY_IAM

# Wait for completion
aws cloudformation wait stack-create-complete \
  --stack-name telegram-bot-YOURNAME

# Get instance IP
aws cloudformation describe-stacks \
  --stack-name telegram-bot-YOURNAME \
  --query 'Stacks[0].Outputs[?OutputKey==`InstanceIP`].OutputValue' \
  --output text
```

### Step 3: Secure File Upload

#### Prepare Deployment Package
```bash
# Clone the deployment repository
git clone https://github.com/your-org/telegram-bot-deploy.git
cd telegram-bot-deploy

# Create your environment file
cp .env.template .env.YOURNAME

# Edit with your credentials (use secure editor)
nano .env.YOURNAME
```

#### Upload Securely
```bash
# Upload with proper permissions
./secure-upload.sh YOUR_INSTANCE_IP your-key.pem .env.YOURNAME
```

### Step 4: Deploy and Start
```bash
# SSH to instance
ssh -i your-key.pem ubuntu@YOUR_INSTANCE_IP

# Run secure deployment
cd /home/<USER>/telegram-bot
chmod +x secure-deploy.sh
./secure-deploy.sh

# Start the bot
sudo systemctl start telegram-bot-YOURNAME
sudo systemctl enable telegram-bot-YOURNAME
```

---

## Security Checklist for Team Members

### Before Deployment
- [ ] Use strong SSH key (RSA 4096 or Ed25519)
- [ ] Verify your IP address is correct
- [ ] Never share credentials in chat/email
- [ ] Use secure file transfer methods

### After Deployment
- [ ] Verify firewall status: `sudo ufw status`
- [ ] Check file permissions: `ls -la /opt/telegram-bot/`
- [ ] Test bot functionality
- [ ] Verify logs don't contain secrets
- [ ] Set up monitoring alerts

### Ongoing Security
- [ ] Regularly update system: `sudo apt update && sudo apt upgrade`
- [ ] Monitor AWS billing dashboard
- [ ] Rotate credentials every 90 days
- [ ] Review access logs monthly

---

## Monitoring & Logging (FREE)

### Built-in Monitoring
```bash
# Check bot health
/opt/telegram-bot/health-check.sh

# View real-time logs
sudo journalctl -u telegram-bot-YOURNAME -f

# System monitoring
/opt/telegram-bot/monitor.sh
```

### Free CloudWatch Integration
- **System metrics**: CPU, memory, disk (free tier)
- **Custom metrics**: Bot uptime, message count
- **Log aggregation**: Centralized logging
- **Alerts**: Email notifications on failures

### Log Rotation (Prevents Disk Full)
```bash
# Automatic log rotation configured
# Keeps 7 days of logs, compresses old logs
# Prevents disk space issues
```

---

## Team Collaboration

### Shared Resources
- **Documentation**: This guide and setup scripts
- **Templates**: Environment file templates
- **Scripts**: Automated deployment scripts
- **Monitoring**: Shared CloudWatch dashboard

### Individual Instances
- **Separate EC2 instances** per team member
- **Isolated environments** (no conflicts)
- **Individual AWS accounts** (cost isolation)
- **Personal credentials** (no sharing)

### Best Practices
- **Naming convention**: `telegram-bot-YOURNAME`
- **Tagging**: Consistent AWS resource tags
- **Documentation**: Update this guide with improvements
- **Security**: Report security issues immediately

---

## Troubleshooting Guide

### Common Issues

#### Bot Won't Start
```bash
# Check logs
sudo journalctl -u telegram-bot-YOURNAME -n 50

# Verify credentials
/opt/telegram-bot/verify-config.sh

# Check file permissions
ls -la /opt/telegram-bot/.env
```

#### Can't Connect to Instance
```bash
# Verify security group
aws ec2 describe-security-groups --group-ids sg-xxxxxxxxx

# Check your current IP
curl ifconfig.me

# Update security group if IP changed
aws ec2 authorize-security-group-ingress \
  --group-id sg-xxxxxxxxx \
  --protocol tcp --port 22 --cidr YOUR_NEW_IP/32
```

#### High AWS Costs
```bash
# Check billing dashboard
aws ce get-cost-and-usage --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY --metrics BlendedCost

# Stop instance if needed
aws ec2 stop-instances --instance-ids i-xxxxxxxxx
```

---

## Team Support

### Getting Help
1. **Check this guide first**
2. **Review troubleshooting section**
3. **Check team chat/Slack**
4. **Contact team lead**

### Reporting Issues
- **Security issues**: Report immediately to team lead
- **Bug reports**: Use issue template
- **Feature requests**: Team discussion first

### Contributing
- **Improvements**: Submit pull requests
- **Documentation**: Keep this guide updated
- **Scripts**: Test thoroughly before sharing

---

## Success Criteria

Your deployment is successful when:
- [ ] Bot responds to trigger messages
- [ ] Admin buttons work in your admin chat
- [ ] Group creation works with all features
- [ ] Service auto-restarts on failure
- [ ] Logs are clean (no errors)
- [ ] AWS costs remain $0
- [ ] Security checklist completed

---

## You're Ready!

Your secure, free-tier Telegram bot is now:
- **Maximum security** with enterprise-grade protection
- **$0 cost** using AWS free tier
- **Auto-restarting** and self-healing
- **Fully monitored** with comprehensive logging
- **Team-ready** with easy replication process

Welcome to the team!

import gspread
from oauth2client.service_account import ServiceAccountCredentials
import os
from dotenv import load_dotenv

load_dotenv()

SHEET_NAME = os.getenv("GOOGLE_SHEET_NAME")

SCOPES = ["https://www.googleapis.com/auth/spreadsheets", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_name("credentials.json", SCOPES)
client = gspread.authorize(creds)
sheet = client.open(SHEET_NAME).sheet1

def get_existing_user_ids():
    try:
        records = sheet.get_all_records()
        return {str(row["User ID"]) for row in records}
    except Exception:
        return set()

def log_lead(user_id, full_name, username, timestamp, bio="", message="", company=None, tags=None):
    user_id = str(user_id)
    existing = get_existing_user_ids()
    if user_id in existing:
        return

    row = [
        user_id,
        username,
        full_name,
        bio,
        message,
        timestamp,
        timestamp,  # last_contact = initial timestamp
        ", ".join(tags or []),
        company or "",
        "FALSE",  # Group Created
        "TRUE",   # Handled
    ]
    sheet.append_row(row)

def update_last_contact(user_id, new_timestamp):
    try:
        user_id = str(user_id)
        all_rows = sheet.get_all_values()
        headers = all_rows[0]
        for i, row in enumerate(all_rows[1:], start=2):
            if row[0] == user_id:
                last_contact_col = headers.index("Last Contact") + 1
                sheet.update_cell(i, last_contact_col, new_timestamp)
                break
    except Exception as e:
        print(f"Error updating last_contact: {e}")

def update_lead_status(user_id, status):
    try:
        user_id = str(user_id)
        records = sheet.get_all_records()
        for i, row in enumerate(records, start=2):
            if str(row.get("User ID")) == user_id:
                sheet.update_cell(i, 5, status)  # Assuming Column E is 'Status'
                return
    except Exception as e:
        print(f"Error updating lead status: {e}")

def append_tag_to_lead(user_id, new_tag):
    try:
        user_id = str(user_id)
        all_rows = sheet.get_all_values()
        headers = all_rows[0]
        tag_index = headers.index("Tags")
        for i, row in enumerate(all_rows[1:], start=2):
            if row[0] == user_id:
                current_tags = row[tag_index].split(", ") if row[tag_index] else []
                if new_tag not in current_tags:
                    current_tags.append(new_tag)
                    updated_tags = ", ".join(current_tags)
                    sheet.update_cell(i, tag_index + 1, updated_tags)
                break
    except Exception as e:
        print(f"Error appending tag: {e}")

def get_lead_data(user_id):
    """Get lead data from Google Sheets for a specific user_id"""
    try:
        user_id = str(user_id)
        all_rows = sheet.get_all_values()
        headers = all_rows[0]

        for row in all_rows[1:]:
            if row[0] == user_id:
                # Map row data to dictionary using headers
                lead_data = {}
                for i, header in enumerate(headers):
                    lead_data[header] = row[i] if i < len(row) else ""

                return {
                    'full_name': lead_data.get('Full Name', ''),
                    'username': lead_data.get('Username', ''),
                    'company': lead_data.get('Company', ''),
                    'tags': lead_data.get('Tags', ''),
                    'bio': lead_data.get('Bio', ''),
                    'message': lead_data.get('Message', '')
                }
        return None
    except Exception as e:
        print(f"Error getting lead data: {e}")
        return None

# 🔐 Telegram Bot Environment Configuration Template
# Copy this file to .env.YOURNAME and fill in your actual values
# NEVER commit this file with real values to version control

# =============================================================================
# TELEGRAM API CREDENTIALS
# Get these from https://my.telegram.org/apps
# =============================================================================
API_ID=YOUR_API_ID_HERE
API_HASH=YOUR_API_HASH_HERE

# =============================================================================
# BOT CONFIGURATION
# Get bot token from @BotFather on Telegram
# =============================================================================
CONTROL_BOT_TOKEN=YOUR_BOT_TOKEN_HERE

# =============================================================================
# ADMIN CONFIGURATION
# Your Telegram user ID and admin group chat ID
# =============================================================================
ADMIN_USER_ID=YOUR_USER_ID_HERE
ADMIN_CHAT_ID=YOUR_ADMIN_CHAT_ID_HERE

# =============================================================================
# GOOGLE SHEETS INTEGRATION
# Name of your Google Sheet for lead tracking
# =============================================================================
GOOGLE_SHEET_NAME=YOUR_SHEET_NAME_HERE

# =============================================================================
# SECURITY NOTES:
# - Keep this file secure (permissions 600)
# - Never share these credentials
# - Use different credentials for each team member
# - Rotate credentials every 90 days
# =============================================================================

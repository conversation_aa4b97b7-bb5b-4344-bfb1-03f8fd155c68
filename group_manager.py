from telethon.tl.functions.messages import CreateChatRequest, ExportChatInviteRequest, EditChatTitleRequest
from telethon.tl.functions.contacts import ResolveUsernameRequest
from telethon.tl.functions.messages import AddChatUserRequest
from telethon import functions
from google_sheets import sheet, get_lead_data
import asyncio
import os
import logging

async def create_lead_group(client, display_name, username, user_id):
    """
    Enhanced group creation with:
    - Smart naming (company name or first name)
    - Logo setting
    - Summary message to admin
    """
    try:
        admin_user_id = int(os.getenv("ADMIN_USER_ID"))
        
        # Get lead data from Google Sheets
        lead_data = get_lead_data(user_id)
        if not lead_data:
            logging.warning(f"No lead data found for user {user_id}")
            lead_data = {
                'full_name': display_name,
                'username': username,
                'company': '',
                'tags': '',
                'bio': '',
                'message': ''
            }

        # Step 1: Determine group name (company name or first name)
        company_name = lead_data.get('company', '').strip()
        first_name = lead_data.get('full_name', display_name).split()[0] if lead_data.get('full_name', display_name) else display_name
        
        if company_name:
            group_title = f"{company_name} / Hacken"
            logging.info(f"Using company name for group: {company_name}")
        else:
            group_title = f"{first_name} / Hacken"
            logging.info(f"Using first name for group: {first_name}")

        # Step 2: Create group with only admin
        result = await client(CreateChatRequest(users=[admin_user_id], title="Temp"))
        group = result.chats[0]
        group_id = group.id
        logging.info(f"Created group with ID: {group_id}")

        # Step 3: Rename group to proper title
        await client(EditChatTitleRequest(chat_id=group_id, title=group_title))
        logging.info(f"Renamed group to: {group_title}")

        # Step 4: Set group logo
        logo_path = "Logo.jpg"
        if os.path.exists(logo_path):
            try:
                # Upload the logo as group photo
                await client(functions.messages.EditChatPhotoRequest(
                    chat_id=group_id,
                    photo=await client.upload_file(logo_path)
                ))
                logging.info("✅ Group logo set successfully")
            except Exception as e:
                logging.warning(f"Failed to set group logo: {e}")
        else:
            logging.warning(f"Logo file not found: {logo_path}")

        # Step 5: Try to add lead user
        try:
            await client(AddChatUserRequest(chat_id=group_id, user_id=user_id, fwd_limit=10))
            added = True
            logging.info(f"✅ User {user_id} added to group directly")
        except Exception as e:
            logging.warning(f"Could not add user directly: {e}")
            added = False

        # Step 6: Generate invite link (always generate for summary)
        invite_link = ""
        try:
            invite = await client(ExportChatInviteRequest(group_id))
            invite_link = invite.link
            logging.info("✅ Invite link generated")
            
            # If user wasn't added directly, send them the invite
            if not added:
                await client.send_message(user_id, f"Couldn't add you directly — here's your private group: {invite_link}")
        except Exception as e:
            logging.error(f"Failed to generate invite link: {e}")

        # Step 7: Update Google Sheet with group status
        all_rows = sheet.get_all_values()
        headers = all_rows[0]
        for i, row in enumerate(all_rows[1:], start=2):
            if row[0] == str(user_id):
                sheet.update_cell(i, headers.index("Group Created") + 1, "TRUE")
                break

        # Step 8: Send welcome message in group
        await asyncio.sleep(1)
        await client.send_message(group_id,
            "Welcome to Hacken – your gateway to Web3 security.\n\n"
            "Since 2017, we've been securing the blockchain space with smart contract audits, "
            "real-time threat detection, and on-chain monitoring.\n\n"
            "🔐 Explore Our Ecosystem:\n"
            "Audits & Services: https://hacken.io\n"
            "Exchange Ratings: https://cer.live\n"
            "Blockchain Intel: https://extractor.live\n"
            "Verified Data: https://trustarmy.io\n\n"
            "Let's build a safer Web3 together."
        )

        # Step 9: Send summary message to admin
        await send_group_summary(client, admin_user_id, group_title, invite_link, lead_data)

        return True

    except Exception as e:
        logging.error(f"Group creation failed: {e}")
        return False

async def send_group_summary(client, admin_user_id, group_name, group_link, lead_data):
    """Send a summary message to admin with group details"""
    try:
        tags = lead_data.get('tags', 'None')
        full_name = lead_data.get('full_name', 'Unknown')
        username = lead_data.get('username', 'N/A')
        company = lead_data.get('company', 'N/A')
        
        summary_message = f"""🎉 **Group Created Successfully!**

👥 **Group Name**: {group_name}
🔗 **Group Link**: {group_link}

👤 **Lead Details**:
• **Name**: {full_name}
• **Username**: @{username}
• **Company**: {company}
• **Tags**: {tags}

✅ Group is ready for engagement!"""

        await client.send_message(admin_user_id, summary_message)
        logging.info("✅ Group summary sent to admin")
        
    except Exception as e:
        logging.error(f"Failed to send group summary: {e}")

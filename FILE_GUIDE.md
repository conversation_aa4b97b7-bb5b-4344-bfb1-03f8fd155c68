# 📁 File Organization Guide

## 🎯 **MINIMAL DEPLOYMENT (Just the Bot)**

If you only want to run the bot locally or on a simple server, you need just these **8 files**:

### ✅ **Core Bot Files (Required)**
```
lead_tracker.py          # Main bot logic
group_manager.py         # Group creation
google_sheets.py         # Sheets integration  
admin_commands.py        # Admin functions
requirements.txt         # Python dependencies
storage.json            # Bot state storage
Logo.jpg                # Group logo
credentials.json        # Google credentials
.env                    # Your secrets (create this)
```

**That's it!** These 9 files are all you need to run the bot.

---

## 🚀 **FULL AWS DEPLOYMENT**

For secure AWS deployment with team support, you need these **15 files**:

### ✅ **Core Bot (9 files above) +**
```
secure-deploy.sh              # AWS deployment script
secure-upload.sh              # Secure file transfer
cloudformation-template.yaml  # AWS infrastructure
README.md                     # Main documentation
TEAM_DEPLOYMENT_GUIDE.md      # Detailed guide
team-setup.sh                 # Team credential helper
cost-monitor.sh               # AWS cost tracking
```

---

## 🗂️ **File Purposes Explained**

### **Bot Core**
- **`lead_tracker.py`** - Detects trigger messages, processes leads, sends admin buttons
- **`group_manager.py`** - Creates groups with company names and logos
- **`google_sheets.py`** - Logs leads to Google Sheets, manages tags
- **`admin_commands.py`** - Handles button clicks, status updates
- **`storage.json`** - Remembers processed users (prevents duplicates)
- **`Logo.jpg`** - Hacken logo for group photos
- **`credentials.json`** - Google service account for Sheets access
- **`requirements.txt`** - Python packages needed

### **AWS Deployment**
- **`secure-deploy.sh`** - Sets up EC2 with security, monitoring, auto-restart
- **`secure-upload.sh`** - Safely transfers files to EC2 with proper permissions
- **`cloudformation-template.yaml`** - Creates AWS infrastructure (EC2, security groups, etc.)

### **Team Tools**
- **`team-setup.sh`** - Interactive script to help teammates get credentials
- **`cost-monitor.sh`** - Tracks AWS usage to stay in free tier
- **Documentation** - Guides for setup and troubleshooting

---

## 🧹 **What I Cleaned Up**

### **Removed (Not Needed)**
- `__pycache__/` - Python cache files (auto-generated)
- `*.session` files - Old Telegram sessions (recreated automatically)
- `venv/` - Local Python environment (recreated on server)
- `get_group_id.py` - Utility script (not needed for production)
- `storage_utils.py` - Unused helper file
- `utils.py` - Unused helper file

---

## 🎯 **Quick Start Options**

### **Option 1: Local Testing (9 files)**
```bash
# Just copy these files:
lead_tracker.py, group_manager.py, google_sheets.py, admin_commands.py
requirements.txt, storage.json, Logo.jpg, credentials.json
# + create .env file

# Run locally:
pip install -r requirements.txt
python lead_tracker.py
```

### **Option 2: AWS Deployment (15 files)**
```bash
# Use all files for full AWS deployment:
./team-setup.sh              # Get credentials
./secure-upload.sh           # Upload to EC2
./secure-deploy.sh           # Deploy on AWS
```

---

## 📦 **For Your Team**

### **Share These Files:**
- All 15 files for complete deployment package
- `team-setup.sh` helps them get their own credentials
- Each person creates their own `.env.THEIRNAME` file
- Everyone gets their own isolated AWS instance

### **Don't Share:**
- Your `.env` file (contains your secrets)
- Your `credentials.json` (your Google account)
- Session files (personal Telegram sessions)

---

## ✅ **Current Clean State**

Your folder now contains exactly what's needed:
- **9 core files** for the bot functionality
- **6 additional files** for AWS deployment and team support
- **0 unnecessary files** (all cleaned up)

**Total: 15 optimized files for a complete, secure, team-ready deployment! 🚀**

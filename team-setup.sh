#!/bin/bash

# 👥 Team Setup Script for Telegram Bot Deployment
# Helps team members get all required information and credentials

set -euo pipefail

# Colors
readonly GREEN='\033[0;32m'
readonly RED='\033[0;31m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}👥 Team Setup - Telegram Bot${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "🔍 Checking prerequisites..."
    
    local missing=()
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        missing+=("AWS CLI")
    fi
    
    # Check SSH
    if ! command -v ssh &> /dev/null; then
        missing+=("SSH client")
    fi
    
    # Check curl
    if ! command -v curl &> /dev/null; then
        missing+=("curl")
    fi
    
    if [[ ${#missing[@]} -gt 0 ]]; then
        error "Missing required tools: ${missing[*]}"
        echo ""
        echo "Install instructions:"
        echo "- AWS CLI: https://aws.amazon.com/cli/"
        echo "- SSH: Usually pre-installed on Linux/Mac"
        echo "- curl: Usually pre-installed on Linux/Mac"
        exit 1
    fi
    
    log "✅ All prerequisites met"
}

# Get team member name
get_team_member_name() {
    echo ""
    echo -e "${BLUE}👤 Team Member Information${NC}"
    echo "Enter your name (letters/numbers only, used for AWS resource naming):"
    read -p "Name: " team_name
    
    # Validate name
    if [[ ! "$team_name" =~ ^[a-zA-Z0-9]+$ ]]; then
        error "Name must contain only letters and numbers"
        exit 1
    fi
    
    if [[ ${#team_name} -gt 20 ]]; then
        error "Name must be 20 characters or less"
        exit 1
    fi
    
    echo "$team_name"
}

# Get current IP
get_current_ip() {
    log "🌐 Getting your current IP address..."
    
    local ip
    ip=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "")
    
    if [[ -z "$ip" ]]; then
        warn "Could not automatically detect your IP"
        echo "Please visit https://whatismyip.com and enter your IP:"
        read -p "Your IP: " ip
    fi
    
    # Validate IP format
    if [[ ! $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        error "Invalid IP address format: $ip"
        exit 1
    fi
    
    log "✅ Your IP: $ip"
    echo "$ip"
}

# Guide for Telegram API credentials
guide_telegram_api() {
    echo ""
    echo -e "${BLUE}📱 Telegram API Credentials${NC}"
    echo "Follow these steps to get your Telegram API credentials:"
    echo ""
    echo "1. Go to https://my.telegram.org"
    echo "2. Log in with your phone number"
    echo "3. Go to 'API Development Tools'"
    echo "4. Create a new application:"
    echo "   - App title: 'Lead Tracker Bot'"
    echo "   - Short name: 'leadbot'"
    echo "   - Platform: 'Desktop'"
    echo "5. Note down your API ID and API Hash"
    echo ""
    read -p "Press Enter when you have your API credentials..."
}

# Guide for Bot Token
guide_bot_token() {
    echo ""
    echo -e "${BLUE}🤖 Bot Token${NC}"
    echo "Follow these steps to create a bot and get the token:"
    echo ""
    echo "1. Open Telegram and message @BotFather"
    echo "2. Send: /newbot"
    echo "3. Choose a name for your bot (e.g., 'Lead Tracker Bot')"
    echo "4. Choose a username ending in 'bot' (e.g., 'leadtracker_bot')"
    echo "5. Copy the bot token (format: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz)"
    echo ""
    read -p "Press Enter when you have your bot token..."
}

# Guide for User ID
guide_user_id() {
    echo ""
    echo -e "${BLUE}👤 Your User ID${NC}"
    echo "Follow these steps to get your Telegram User ID:"
    echo ""
    echo "1. Open Telegram and message @userinfobot"
    echo "2. Send any message to the bot"
    echo "3. The bot will reply with your user information"
    echo "4. Copy your User ID (numeric value)"
    echo ""
    read -p "Press Enter when you have your User ID..."
}

# Guide for Admin Chat ID
guide_admin_chat() {
    echo ""
    echo -e "${BLUE}💬 Admin Chat ID${NC}"
    echo "Follow these steps to get your admin group chat ID:"
    echo ""
    echo "1. Create a group in Telegram for admin notifications"
    echo "2. Add your bot to the group"
    echo "3. Send a test message in the group"
    echo "4. Visit: https://api.telegram.org/botYOUR_BOT_TOKEN/getUpdates"
    echo "   (Replace YOUR_BOT_TOKEN with your actual bot token)"
    echo "5. Look for 'chat':{'id':-1234567890} (negative number for groups)"
    echo "6. Copy the chat ID (including the minus sign)"
    echo ""
    read -p "Press Enter when you have your admin chat ID..."
}

# Guide for Google Sheets
guide_google_sheets() {
    echo ""
    echo -e "${BLUE}📊 Google Sheets Setup${NC}"
    echo "Follow these steps to set up Google Sheets integration:"
    echo ""
    echo "1. Go to Google Cloud Console (console.cloud.google.com)"
    echo "2. Create a new project or select existing one"
    echo "3. Enable Google Sheets API"
    echo "4. Create a Service Account:"
    echo "   - Go to IAM & Admin > Service Accounts"
    echo "   - Click 'Create Service Account'"
    echo "   - Name: 'telegram-bot-sheets'"
    echo "   - Role: 'Editor' (or custom with Sheets access)"
    echo "5. Create and download JSON key file"
    echo "6. Rename the file to 'credentials.json'"
    echo "7. Create a Google Sheet for lead tracking"
    echo "8. Share the sheet with the service account email"
    echo "9. Note the sheet name"
    echo ""
    read -p "Press Enter when you have completed Google Sheets setup..."
}

# Create environment file
create_env_file() {
    local team_name="$1"
    local env_file=".env.$team_name"
    
    echo ""
    echo -e "${BLUE}📝 Creating Environment File${NC}"
    
    if [[ -f "$env_file" ]]; then
        warn "Environment file $env_file already exists"
        read -p "Overwrite? (y/N): " overwrite
        if [[ "$overwrite" != "y" && "$overwrite" != "Y" ]]; then
            log "Keeping existing file"
            return
        fi
    fi
    
    # Copy template
    cp .env.template "$env_file"
    chmod 600 "$env_file"
    
    log "✅ Created $env_file"
    log "📝 Please edit this file with your actual credentials"
    log "🔐 File permissions set to 600 (secure)"
    
    echo ""
    echo "Next steps:"
    echo "1. Edit $env_file with your credentials"
    echo "2. Place credentials.json in this directory"
    echo "3. Run the deployment commands"
}

# Generate deployment commands
generate_commands() {
    local team_name="$1"
    local ip="$2"
    
    echo ""
    echo -e "${BLUE}🚀 Deployment Commands${NC}"
    echo "Copy and run these commands to deploy your bot:"
    echo ""
    
    echo "# 1. Deploy AWS infrastructure"
    echo "aws cloudformation create-stack \\"
    echo "  --stack-name telegram-bot-$team_name \\"
    echo "  --template-body file://cloudformation-template.yaml \\"
    echo "  --parameters ParameterKey=KeyPairName,ParameterValue=YOUR_KEY_PAIR_NAME \\"
    echo "               ParameterKey=AllowedSSHIP,ParameterValue=$ip/32 \\"
    echo "               ParameterKey=TeamMemberName,ParameterValue=$team_name \\"
    echo "  --capabilities CAPABILITY_IAM"
    echo ""
    
    echo "# 2. Wait for deployment to complete"
    echo "aws cloudformation wait stack-create-complete --stack-name telegram-bot-$team_name"
    echo ""
    
    echo "# 3. Get instance IP"
    echo "aws cloudformation describe-stacks --stack-name telegram-bot-$team_name \\"
    echo "  --query 'Stacks[0].Outputs[?OutputKey==\`InstanceIP\`].OutputValue' --output text"
    echo ""
    
    echo "# 4. Upload files securely (replace INSTANCE_IP with actual IP)"
    echo "./secure-upload.sh INSTANCE_IP YOUR_KEY_PAIR.pem .env.$team_name"
    echo ""
    
    echo "# 5. SSH and deploy"
    echo "ssh -i YOUR_KEY_PAIR.pem ubuntu@INSTANCE_IP"
    echo "cd /home/<USER>/telegram-bot"
    echo "chmod +x secure-deploy.sh"
    echo "./secure-deploy.sh"
    echo "sudo systemctl start telegram-bot"
    echo ""
}

# Main function
main() {
    print_header
    
    log "This script will help you gather all required information for deploying your Telegram bot."
    echo ""
    
    check_prerequisites
    
    local team_name=$(get_team_member_name)
    local ip=$(get_current_ip)
    
    guide_telegram_api
    guide_bot_token
    guide_user_id
    guide_admin_chat
    guide_google_sheets
    
    create_env_file "$team_name"
    generate_commands "$team_name" "$ip"
    
    echo ""
    log "✅ Setup complete!"
    log "📋 Follow the deployment commands above to deploy your bot"
    log "💡 Keep your credentials secure and never share them"
    log "🆘 If you need help, check the TEAM_DEPLOYMENT_GUIDE.md"
    echo ""
}

# Run if executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

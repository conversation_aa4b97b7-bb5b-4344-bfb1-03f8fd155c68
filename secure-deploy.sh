#!/bin/bash

# Secure Telegram Bot Deployment Script
# Optimized for maximum security, zero cost, and team usability

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Configuration
readonly APP_DIR="/opt/telegram-bot"
readonly SERVICE_NAME="telegram-bot"
readonly LOG_DIR="/var/log/telegram-bot"
readonly BACKUP_DIR="/opt/telegram-bot/backups"
readonly USER="ubuntu"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a /tmp/deployment.log
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a /tmp/deployment.log
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a /tmp/deployment.log
    exit 1
}

# Security checks
security_check() {
    log "Running security checks..."

    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        error "Don't run this script as root. Use sudo only when needed."
    fi

    # Check if .env file exists and has proper permissions
    if [[ -f ".env" ]]; then
        chmod 600 .env
        log "Environment file secured"
    else
        error ".env file not found. Please create it first."
    fi

    # Check for credentials.json
    if [[ -f "credentials.json" ]]; then
        chmod 600 credentials.json
        log "Google credentials secured"
    else
        warn "credentials.json not found. Google Sheets may not work."
    fi
}

# System hardening
harden_system() {
    log "Hardening system security..."
    
    # Update system
    sudo apt update && sudo apt upgrade -y
    
    # Install security tools
    sudo apt install -y ufw fail2ban unattended-upgrades
    
    # Configure automatic security updates
    echo 'Unattended-Upgrade::Automatic-Reboot "false";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades
    echo 'Unattended-Upgrade::Remove-Unused-Dependencies "true";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades
    
    # Configure firewall
    sudo ufw --force reset
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    sudo ufw allow ssh
    sudo ufw allow out 443  # HTTPS for Telegram API
    sudo ufw allow out 53   # DNS
    sudo ufw --force enable
    
    # Configure fail2ban
    sudo systemctl enable fail2ban
    sudo systemctl start fail2ban
    
    log "System hardened"
}

# Install dependencies
install_dependencies() {
    log "📦 Installing dependencies..."
    
    # Install Python and essential tools
    sudo apt install -y python3 python3-pip python3-venv git curl htop
    
    # Create application directory with proper permissions
    sudo mkdir -p $APP_DIR $LOG_DIR $BACKUP_DIR
    sudo chown -R $USER:$USER $APP_DIR $BACKUP_DIR
    sudo chown -R syslog:adm $LOG_DIR
    
    log "Dependencies installed"
}

# Setup Python environment
setup_python() {
    log "Setting up Python environment..."
    
    cd $APP_DIR
    
    # Create virtual environment
    python3 -m venv venv
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install requirements
    if [[ -f "/home/<USER>/telegram-bot/requirements.txt" ]]; then
        pip install -r /home/<USER>/telegram-bot/requirements.txt
    else
        # Install essential packages
        pip install telethon python-dotenv gspread oauth2client aiohttp psutil
    fi
    
    log "Python environment ready"
}

# Copy and secure files
secure_files() {
    log "📁 Copying and securing files..."
    
    # Copy all files from upload directory
    if [[ -d "/home/<USER>/telegram-bot" ]]; then
        cp -r /home/<USER>/telegram-bot/* $APP_DIR/
    else
        error "Source files not found in /home/<USER>/telegram-bot"
    fi

    # Set proper ownership
    sudo chown -R $USER:$USER $APP_DIR

    # Secure sensitive files
    chmod 600 $APP_DIR/.env 2>/dev/null || warn ".env file not found"
    chmod 600 $APP_DIR/credentials.json 2>/dev/null || warn "credentials.json not found"
    chmod 644 $APP_DIR/*.py
    chmod 755 $APP_DIR/*.sh 2>/dev/null || true

    # Remove any backup or temporary files
    find $APP_DIR -name "*.bak" -delete 2>/dev/null || true
    find $APP_DIR -name "*~" -delete 2>/dev/null || true
    find $APP_DIR -name ".DS_Store" -delete 2>/dev/null || true

    log "Files secured"
}

# Create monitoring scripts
create_monitoring() {
    log "Creating monitoring scripts..."

    # Health check script
    cat > $APP_DIR/health-check.sh << 'EOF'
#!/bin/bash
SERVICE_NAME="telegram-bot"
LOG_FILE="/var/log/telegram-bot/health.log"

check_service() {
    if systemctl is-active --quiet $SERVICE_NAME; then
        echo "$(date): Service running" >> $LOG_FILE
        return 0
    else
        echo "$(date): Service down" >> $LOG_FILE
        return 1
    fi
}

check_resources() {
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    DISK_USAGE=$(df /opt/telegram-bot | tail -1 | awk '{print $5}' | sed 's/%//')

    echo "$(date): Memory: ${MEM_USAGE}%, Disk: ${DISK_USAGE}%" >> $LOG_FILE

    if (( $(echo "$MEM_USAGE > 80" | bc -l) )); then
        echo "$(date): High memory usage: ${MEM_USAGE}%" >> $LOG_FILE
    fi

    if [ "$DISK_USAGE" -gt 80 ]; then
        echo "$(date): High disk usage: ${DISK_USAGE}%" >> $LOG_FILE
    fi
}

main() {
    check_service && check_resources
    
    # Keep only last 1000 lines of health log
    tail -n 1000 $LOG_FILE > ${LOG_FILE}.tmp && mv ${LOG_FILE}.tmp $LOG_FILE
}

main
EOF

    # System monitor script
    cat > $APP_DIR/monitor.sh << 'EOF'
#!/bin/bash
echo "Telegram Bot System Status"
echo "================================"
echo "Service Status: $(systemctl is-active telegram-bot)"
echo "Memory Usage: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "Disk Usage: $(df -h /opt/telegram-bot | tail -1 | awk '{print $5}')"
echo "Uptime: $(uptime -p)"
echo ""
echo "Recent Logs:"
sudo journalctl -u telegram-bot --no-pager -n 5
EOF

    # Backup script
    cat > $APP_DIR/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/telegram-bot/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="bot_backup_$DATE.tar.gz"

echo "Creating backup..."
tar -czf $BACKUP_DIR/$BACKUP_FILE \
    --exclude='venv' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    --exclude='logs' \
    /opt/telegram-bot/

echo "Backup created: $BACKUP_FILE"

# Keep only last 5 backups
cd $BACKUP_DIR
ls -t bot_backup_*.tar.gz | tail -n +6 | xargs -r rm
echo "Old backups cleaned"
EOF

    chmod +x $APP_DIR/*.sh
    log "Monitoring scripts created"
}

# Create systemd service
create_service() {
    log "Creating systemd service..."
    
    # Create service wrapper
    cat > $APP_DIR/run_bot.py << 'EOF'
#!/usr/bin/env python3
import sys
import os
import logging
from pathlib import Path

# Set up logging
log_dir = Path("/var/log/telegram-bot")
log_dir.mkdir(exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / "bot.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    try:
        # Change to app directory
        os.chdir("/opt/telegram-bot")
        
        # Import and run bot
        logger.info("Starting Telegram Lead Tracker Bot...")
        from lead_tracker import main as bot_main
        import asyncio
        asyncio.run(bot_main())
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

    chmod +x $APP_DIR/run_bot.py
    
    # Create systemd service file
    sudo tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null << EOF
[Unit]
Description=Telegram Lead Tracker Bot
After=network-online.target
Wants=network-online.target
StartLimitIntervalSec=0

[Service]
Type=simple
User=$USER
Group=$USER
WorkingDirectory=$APP_DIR
Environment=PATH=$APP_DIR/venv/bin
ExecStart=$APP_DIR/venv/bin/python $APP_DIR/run_bot.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$APP_DIR /var/log/telegram-bot
MemoryMax=256M
TasksMax=50

[Install]
WantedBy=multi-user.target
EOF

    # Enable service
    sudo systemctl daemon-reload
    sudo systemctl enable $SERVICE_NAME
    
    log "✅ Service created and enabled"
}

# Setup log rotation
setup_logging() {
    log "📝 Setting up log rotation..."
    
    # Create logrotate configuration
    sudo tee /etc/logrotate.d/telegram-bot > /dev/null << EOF
/var/log/telegram-bot/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        systemctl reload telegram-bot > /dev/null 2>&1 || true
    endscript
}
EOF

    log "✅ Log rotation configured"
}

# Final security check
final_security_check() {
    log "🔒 Final security verification..."
    
    # Check file permissions
    local issues=0
    
    if [[ -f "$APP_DIR/.env" ]]; then
        local perm=$(stat -c "%a" "$APP_DIR/.env")
        if [[ "$perm" != "600" ]]; then
            warn "⚠️  .env file permissions: $perm (should be 600)"
            ((issues++))
        fi
    fi
    
    # Check firewall
    if ! sudo ufw status | grep -q "Status: active"; then
        warn "⚠️  Firewall not active"
        ((issues++))
    fi
    
    # Check service user
    if [[ $(stat -c "%U" "$APP_DIR") != "$USER" ]]; then
        warn "⚠️  App directory not owned by $USER"
        ((issues++))
    fi
    
    if [[ $issues -eq 0 ]]; then
        log "✅ All security checks passed"
    else
        warn "⚠️  $issues security issues found"
    fi
}

# Main deployment function
main() {
    log "🚀 Starting secure deployment..."
    
    security_check
    harden_system
    install_dependencies
    setup_python
    secure_files
    create_monitoring
    create_service
    setup_logging
    final_security_check
    
    log "✅ Deployment completed successfully!"
    log ""
    log "📋 Next steps:"
    log "1. Start the service: sudo systemctl start $SERVICE_NAME"
    log "2. Check status: sudo systemctl status $SERVICE_NAME"
    log "3. View logs: sudo journalctl -u $SERVICE_NAME -f"
    log "4. Monitor health: $APP_DIR/health-check.sh"
    log ""
    log "🎉 Your secure Telegram bot is ready!"
}

# Run main function
main "$@"

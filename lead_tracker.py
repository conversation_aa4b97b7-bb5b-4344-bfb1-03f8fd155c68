import os
import asyncio
import json
import logging
from dotenv import load_dotenv
from telethon import TelegramClient, events, Button
from datetime import datetime, timezone
from google_sheets import log_lead, update_lead_status, append_tag_to_lead
from telethon.tl.functions.users import GetFullUserRequest

load_dotenv()

API_ID = int(os.getenv("API_ID"))
API_HASH = os.getenv("API_HASH")
ADMIN_CHAT_ID = int(os.getenv("ADMIN_CHAT_ID"))
CONTROL_BOT_TOKEN = os.getenv("CONTROL_BOT_TOKEN")
SESSION_NAME = "session"

client = TelegramClient(SESSION_NAME, API_ID, API_HASH)
# Bot client for sending admin buttons (only bots can send inline buttons)
bot_client = TelegramClient("bot_session", API_ID, API_HASH)

STORAGE_PATH = "storage.json"
if os.path.exists(STORAGE_PATH):
    with open(STORAGE_PATH, "r") as f:
        storage_data = json.load(f)
        handled_users = storage_data.get("handled_user_ids", [])
        initiated_users = storage_data.get("initiated_user_ids", [])
else:
    handled_users = []
    initiated_users = []

def save_storage():
    with open(STORAGE_PATH, "w") as f:
        json.dump({
            "handled_user_ids": handled_users,
            "initiated_user_ids": initiated_users
        }, f)

def infer_tags(text):
    tags = []
    lowered = text.lower()
    if "audit" in lowered:
        tags.append("Audit")
    if "bug" in lowered or "bounty" in lowered:
        tags.append("Bug Bounty")
    if "monitoring" in lowered:
        tags.append("Monitoring")
    if "pen test" in lowered:
        tags.append("Pen Test")
    return tags

def infer_company(text, sender):
    if sender.last_name and sender.last_name.lower() not in sender.first_name.lower():
        return sender.last_name
    if "@" not in text and len(text.split()) <= 3:
        return text.strip()
    return ""

async def send_admin_buttons(user_id, username, full_name):
    logging.info(f"Sending admin buttons for user: {user_id}")

    # Ensure bot client is started
    if not bot_client.is_connected():
        await bot_client.start(bot_token=CONTROL_BOT_TOKEN)

    buttons = [
        [
            Button.inline("Tag: Audit", data=f"tag_audit:{user_id}"),
            Button.inline("Tag: Pen Test", data=f"tag_pen_test:{user_id}"),
            Button.inline("Tag: Bug Bounty", data=f"tag_bug:{user_id}"),
            Button.inline("Tag: Monitoring", data=f"tag_monitoring:{user_id}")
        ],
        [
            Button.inline("Cold → Warm", data=f"status_warm:{user_id}"),
            Button.inline("Create Group", data=f"create_group:{user_id}"),
            Button.inline("Export Lead", data=f"export:{user_id}")
        ]
    ]

    try:
        # Use bot client to send interactive buttons to admin chat
        await bot_client.send_message(
            ADMIN_CHAT_ID,
            f"🔔 Lead logged: {full_name} (@{username})",
            buttons=buttons
        )
        logging.info(f"✅ Admin buttons sent successfully to {ADMIN_CHAT_ID} via bot")
    except Exception as e:
        logging.error(f"Failed to send admin buttons via bot: {e}")
        # Fallback: try sending to ADMIN_USER_ID instead
        try:
            ADMIN_USER_ID = int(os.getenv("ADMIN_USER_ID"))
            await bot_client.send_message(
                ADMIN_USER_ID,
                f"🔔 Lead logged: {full_name} (@{username})",
                buttons=buttons
            )
            logging.info(f"✅ Admin buttons sent successfully to ADMIN_USER_ID {ADMIN_USER_ID} as fallback")
        except Exception as e2:
            logging.error(f"Fallback also failed: {e2}")

@client.on(events.NewMessage(outgoing=True))
async def handle_outgoing_message(event):
    if event.is_group or event.is_channel:
        return

    message_text = event.raw_text.strip()
    logging.info(f"📤 Outgoing message detected: '{message_text}'")

    trigger_words = ["hello", "alex", "hacken"]
    message_lower = message_text.lower()
    logging.info(f"🔍 Checking for trigger words {trigger_words} in: '{message_lower}'")

    if all(word in message_lower for word in trigger_words):
        logging.info("🎯 TRIGGER DETECTED! Starting lead flow...")
        chat = await event.get_chat()
        user_id = chat.id

        if user_id not in initiated_users:
            initiated_users.append(user_id)
            save_storage()
            logging.info(f"Initiated contact with user {user_id}")

        if user_id in handled_users:
            logging.info(f"User {user_id} already handled - skipping flow")
            return

        try:
            entity = await client.get_entity(user_id)
            full_name = f"{entity.first_name or ''} {entity.last_name or ''}".strip()
            username = entity.username or ""

            try:
                full = await client(GetFullUserRequest(user_id))
                bio = full.full_user.about or ""
            except Exception as e:
                bio = ""
                logging.warning(f"Failed to fetch user bio: {e}")

            messages = await client.get_messages(user_id, limit=1)
            last_message = messages[0].message if messages else ""

            timestamp = datetime.now(timezone.utc).isoformat()
            tags = infer_tags(last_message + " " + bio)
            company = infer_company(last_message + " " + bio, entity)

            logging.info(f"Processing lead for {full_name} ({username}) — Tags: {tags}, Company: {company}")

            log_lead(user_id, full_name, username, timestamp, bio=bio, message=last_message, company=company, tags=tags)
            handled_users.append(user_id)
            save_storage()

            await asyncio.sleep(3)

            await event.respond(
                "Hacken\n"
                "From pre-launch audits to post-deployment monitoring, we help Web3 teams build securely from day one.\n"
                "Your roadmap is unique. So is our approach.\n"
                "https://hacken.io"
            )

            await send_admin_buttons(user_id, username, full_name)

        except Exception as e:
            logging.error(f"Error processing lead flow for {user_id}: {e}")

@client.on(events.NewMessage(incoming=True))
async def handle_incoming_message(event):
    if event.is_group or event.is_channel:
        return

    sender = await event.get_sender()
    if sender.bot:
        return

    message_text = event.raw_text.strip()
    user_id = sender.id

    logging.info(f"New message from {user_id}: {message_text}")

    if user_id in initiated_users:
        logging.info(f"Additional message from initiated user {user_id}")
    else:
        logging.info(f"Message from non-initiated user {user_id} - ignoring")

@bot_client.on(events.CallbackQuery)
async def handle_callback(event):
    data = event.data.decode("utf-8")
    if ":" not in data:
        return

    action, user_id = data.split(":")
    logging.info(f"Callback received: {action} for user {user_id}")

    # Check if sender is authorized (either ADMIN_CHAT_ID or ADMIN_USER_ID)
    ADMIN_USER_ID = int(os.getenv("ADMIN_USER_ID"))
    if event.sender_id != ADMIN_CHAT_ID and event.sender_id != ADMIN_USER_ID:
        await event.answer("Access denied", alert=True)
        logging.warning(f"Unauthorized callback attempt from {event.sender_id}")
        return

    if action == "tag_audit":
        append_tag_to_lead(user_id, "Audit")
        await event.answer("Tagged as Audit")
    elif action == "tag_pen_test":
        append_tag_to_lead(user_id, "Pen Test")
        await event.answer("Tagged as Pen Test")
    elif action == "tag_bug":
        append_tag_to_lead(user_id, "Bug Bounty")
        await event.answer("Tagged as Bug Bounty")
    elif action == "tag_monitoring":
        append_tag_to_lead(user_id, "Monitoring")
        await event.answer("Tagged as Monitoring")
    elif action == "status_warm":
        update_lead_status(user_id, "Warm Lead")
        await event.answer("Status updated to Warm")
    elif action == "export":
        await event.answer("Exporting... (not implemented)", alert=True)
    elif action == "create_group":
        from group_manager import create_lead_group
        full_name = "Unknown"
        username = ""
        try:
            entity = await client.get_entity(int(user_id))
            full_name = f"{entity.first_name or ''} {entity.last_name or ''}".strip()
            username = entity.username or ""
        except Exception as e:
            logging.warning(f"Failed to resolve user entity for group creation: {e}")
        await create_lead_group(client, full_name, username, int(user_id))
        await event.answer("Group created")

async def main():
    """Start both user client and bot client"""
    logging.basicConfig(level=logging.INFO)
    print("Lead tracker running...")

    # Start user client for monitoring messages
    await client.start()
    print("✅ User client started")

    # Start bot client for sending admin buttons
    await bot_client.start(bot_token=CONTROL_BOT_TOKEN)
    print("✅ Bot client started")

    print("🚀 Both clients running - ready to process leads!")

    # Run until disconnected
    await client.run_until_disconnected()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())

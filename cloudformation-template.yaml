AWSTemplateFormatVersion: '2010-09-09'
Description: 'Secure Telegram Bot Infrastructure - FREE TIER OPTIMIZED'

Parameters:
  KeyPairName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: Name of an existing EC2 KeyPair for SSH access

  AllowedSSHIP:
    Type: String
    Description: Your current IP address for SSH access (get from whatismyip.com)
    AllowedPattern: '^([0-9]{1,3}\.){3}[0-9]{1,3}/32$'
    ConstraintDescription: Must be a valid IP address in CIDR format (e.g., *******/32)

  TeamMemberName:
    Type: String
    Description: Your name for resource tagging (e.g., john, sarah)
    AllowedPattern: '^[a-zA-Z0-9]+$'
    ConstraintDescription: Only letters and numbers allowed
    MaxLength: 20

Resources:
  # IAM Role for EC2 Instance (Minimal permissions for free tier)
  TelegramBotRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'TelegramBotRole-${TeamMemberName}'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: MinimalCloudWatchAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/ec2/telegram-bot-${TeamMemberName}*'

  # Instance Profile
  TelegramBotInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Roles:
        - !Ref TelegramBotRole

  # Security Group (Maximum security)
  TelegramBotSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub 'TelegramBotSG-${TeamMemberName}'
      GroupDescription: !Sub 'Secure Telegram Bot SG for ${TeamMemberName}'
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: !Ref AllowedSSHIP
          Description: SSH access from team member IP only
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
          Description: HTTPS for Telegram API
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
          Description: HTTP for system updates
        - IpProtocol: tcp
          FromPort: 53
          ToPort: 53
          CidrIp: 0.0.0.0/0
          Description: DNS TCP
        - IpProtocol: udp
          FromPort: 53
          ToPort: 53
          CidrIp: 0.0.0.0/0
          Description: DNS UDP
      Tags:
        - Key: Name
          Value: !Sub 'TelegramBot-SG-${TeamMemberName}'
        - Key: Owner
          Value: !Ref TeamMemberName
        - Key: Purpose
          Value: TelegramBot

  # EC2 Instance (FREE TIER OPTIMIZED)
  TelegramBotInstance:
    Type: AWS::EC2::Instance
    Properties:
      ImageId: ami-0c02fb55956c7d316  # Ubuntu 22.04 LTS (update for your region)
      InstanceType: t2.micro  # FREE TIER ELIGIBLE
      KeyName: !Ref KeyPairName
      IamInstanceProfile: !Ref TelegramBotInstanceProfile
      SecurityGroupIds:
        - !Ref TelegramBotSecurityGroup
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeSize: 8  # FREE TIER: 30GB total allowed
            VolumeType: gp2  # FREE TIER: gp2 only
            Encrypted: true
            DeleteOnTermination: true
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          # Basic setup for free tier
          apt-get update
          apt-get install -y python3 python3-pip python3-venv git curl

          # Create application directory
          mkdir -p /opt/telegram-bot /var/log/telegram-bot
          chown ubuntu:ubuntu /opt/telegram-bot

          # Configure automatic security updates only
          apt-get install -y unattended-upgrades
          echo 'Unattended-Upgrade::Automatic-Reboot "false";' >> /etc/apt/apt.conf.d/50unattended-upgrades

          # Create ready indicator
          touch /home/<USER>/instance-ready
          chown ubuntu:ubuntu /home/<USER>/instance-ready

      Tags:
        - Key: Name
          Value: !Sub 'TelegramBot-${TeamMemberName}'
        - Key: Owner
          Value: !Ref TeamMemberName
        - Key: Environment
          Value: Production
        - Key: Application
          Value: TelegramBot
        - Key: CostCenter
          Value: FreeTier

  # CloudWatch Log Group (FREE TIER)
  TelegramBotLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ec2/telegram-bot-${TeamMemberName}'
      RetentionInDays: 7  # FREE TIER: 5GB storage, keep logs short
      Tags:
        - Key: Owner
          Value: !Ref TeamMemberName
        - Key: Purpose
          Value: TelegramBot

  # Elastic IP (FREE TIER: 1 free when attached)
  TelegramBotEIP:
    Type: AWS::EC2::EIP
    Properties:
      InstanceId: !Ref TelegramBotInstance
      Tags:
        - Key: Name
          Value: !Sub 'TelegramBot-EIP-${TeamMemberName}'
        - Key: Owner
          Value: !Ref TeamMemberName

Outputs:
  InstanceId:
    Description: Instance ID of the Telegram Bot server
    Value: !Ref TelegramBotInstance
    Export:
      Name: !Sub '${AWS::StackName}-InstanceId'

  InstanceIP:
    Description: Public IP address of the Telegram Bot server
    Value: !Ref TelegramBotEIP
    Export:
      Name: !Sub '${AWS::StackName}-PublicIP'

  SSHCommand:
    Description: SSH command to connect to the instance
    Value: !Sub 'ssh -i ${KeyPairName}.pem ubuntu@${TelegramBotEIP}'

  UploadCommand:
    Description: Command to upload files securely
    Value: !Sub './secure-upload.sh ${TelegramBotEIP} ${KeyPairName}.pem .env.${TeamMemberName}'

  SecurityGroupId:
    Description: Security Group ID
    Value: !Ref TelegramBotSecurityGroup

  TeamMember:
    Description: Team member name for this deployment
    Value: !Ref TeamMemberName

  CostEstimate:
    Description: Estimated monthly cost (FREE TIER)
    Value: '$0.00 (Free Tier Eligible for 12 months)'

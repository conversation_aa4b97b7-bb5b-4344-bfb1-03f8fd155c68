# Secure Telegram Lead Tracker Bot - Team Deployment

## Overview

A production-ready, secure Telegram bot optimized for AWS Free Tier with enterprise-grade security, auto-restart capabilities, and team-friendly deployment process.

### Key Features
- $0 AWS costs (12-month free tier eligible)
- Maximum security (encrypted storage, firewall, restricted access)
- Auto-restart on failures and boot-time startup
- Team collaboration ready with isolated deployments
- Complete monitoring with free CloudWatch integration
- Easy deployment with automated scripts

---

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Telegram API  │◄──►│   EC2 Instance   │◄──►│  Google Sheets  │
│                 │    │                  │    │                 │
│ • Bot Messages  │    │ • Lead Tracker   │    │ • Lead Storage  │
│ • Admin Buttons │    │ • Auto-restart   │    │ • Tag Management│
│ • Group Creation│    │ • Monitoring     │    │ • Export Data   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   CloudWatch     │
                       │                  │
                       │ • Logs (7 days)  │
                       │ • Metrics        │
                       │ • Alerts         │
                       └──────────────────┘
```

---

## Security Features

### Network Security
- **Firewall**: UFW with minimal rules (SSH from your IP only)
- **Security Groups**: Restricted inbound (SSH), limited outbound (HTTPS/DNS)
- **No open ports** except SSH from authorized IP

### Application Security
- **Non-root execution**: <PERSON><PERSON> runs as `ubuntu` user
- **File permissions**: 600 for sensitive files, 644 for code
- **Process isolation**: Systemd security sandbox
- **Encrypted storage**: EBS volumes encrypted at rest

### Access Control
- **Admin-only functions**: Restricted to configured user/chat IDs
- **SSH key authentication**: No password login allowed
- **Credential isolation**: Each team member uses separate credentials

---

## Cost Optimization (FREE TIER)

### AWS Free Tier Limits
- **EC2**: 750 hours/month t2.micro (24/7 for 31 days)
- **EBS**: 30GB storage (we use 8GB)
- **Data Transfer**: 15GB outbound (plenty for bot traffic)
- **CloudWatch**: 10 custom metrics, 5GB logs

### Monitoring Tools
- **cost-monitor.sh**: Track usage against free tier limits
- **Billing alerts**: Automated notifications
- **Resource optimization**: Automated cleanup scripts

---

## Team Setup Process

### 1. Prerequisites
```bash
# Install required tools
# - AWS CLI
# - SSH client
# - Git (optional)
```

### 2. Get Your Credentials
```bash
# Run the team setup script
chmod +x team-setup.sh
./team-setup.sh
```

This interactive script guides you through:
- Telegram API credentials
- Bot token creation
- User ID discovery
- Admin chat setup
- Google Sheets integration

### 3. Deploy Infrastructure
```bash
# Deploy your personal instance
aws cloudformation create-stack \
  --stack-name telegram-bot-YOURNAME \
  --template-body file://cloudformation-template.yaml \
  --parameters ParameterKey=KeyPairName,ParameterValue=YOUR_KEY \
               ParameterKey=AllowedSSHIP,ParameterValue=YOUR_IP/32 \
               ParameterKey=TeamMemberName,ParameterValue=YOURNAME \
  --capabilities CAPABILITY_IAM
```

### 4. Upload and Deploy
```bash
# Upload files securely
./secure-upload.sh INSTANCE_IP your-key.pem .env.YOURNAME

# SSH and deploy
ssh -i your-key.pem ubuntu@INSTANCE_IP
cd /home/<USER>/telegram-bot
chmod +x secure-deploy.sh
./secure-deploy.sh

# Start the bot
sudo systemctl start telegram-bot
```

---

## File Structure

```
telegram-bot/
├── Bot Files
│   ├── lead_tracker.py          # Main bot logic
│   ├── group_manager.py         # Group creation
│   ├── google_sheets.py         # Sheets integration
│   ├── admin_commands.py        # Admin functions
│   └── storage.json             # Local storage
│
├── Deployment
│   ├── secure-deploy.sh         # Main deployment script
│   ├── secure-upload.sh         # Secure file transfer
│   ├── cloudformation-template.yaml # AWS infrastructure
│   └── requirements.txt         # Python dependencies
│
├── Team Setup
│   ├── team-setup.sh           # Interactive setup guide
│   ├── .env.template           # Environment template
│   └── cost-monitor.sh         # Cost tracking
│
├── Assets
│   ├── Logo.jpg                # Group logo
│   ├── credentials.json        # Google service account
│   └── .env.YOURNAME          # Your environment file
│
└── Documentation
    ├── README.md               # This file
    └── TEAM_DEPLOYMENT_GUIDE.md # Detailed guide
```

---

## Management Commands

### Service Control
```bash
sudo systemctl start telegram-bot      # Start bot
sudo systemctl stop telegram-bot       # Stop bot
sudo systemctl restart telegram-bot    # Restart bot
sudo systemctl status telegram-bot     # Check status
```

### Monitoring
```bash
sudo journalctl -u telegram-bot -f     # Live logs
/opt/telegram-bot/health-check.sh      # Health status
/opt/telegram-bot/monitor.sh           # System resources
./cost-monitor.sh                      # AWS costs
```

### Maintenance
```bash
/opt/telegram-bot/backup.sh            # Create backup
sudo apt update && sudo apt upgrade    # Update system
```

---

## Testing Your Bot

### 1. Trigger Lead Flow
Send a message containing "hello alex hacken" to any Telegram user

### 2. Expected Behavior
- Bot detects outgoing message
- Processes lead information
- Logs to Google Sheets
- Sends Hacken response message
- Shows admin buttons in your admin group

### 3. Admin Functions
- **Tag buttons**: Audit, Pen Test, Bug Bounty, Monitoring
- **Status update**: Cold → Warm
- **Group creation**: Auto-named with company/first name + logo
- **Export**: Lead data export

---

## Troubleshooting

### Bot Not Starting
```bash
# Check logs
sudo journalctl -u telegram-bot -n 50

# Verify environment
cat /opt/telegram-bot/.env

# Check permissions
ls -la /opt/telegram-bot/
```

### Can't Connect to Instance
```bash
# Check your current IP
curl ifconfig.me

# Update security group if IP changed
aws ec2 authorize-security-group-ingress \
  --group-id sg-xxxxxxxxx \
  --protocol tcp --port 22 --cidr NEW_IP/32
```

### High AWS Costs
```bash
# Monitor costs
./cost-monitor.sh

# Stop instance if needed
aws ec2 stop-instances --instance-ids i-xxxxxxxxx
```

---

## Monitoring & Alerts

### Built-in Monitoring
- **Health checks**: Automated every 5 minutes
- **Resource monitoring**: CPU, memory, disk usage
- **Log rotation**: Prevents disk space issues
- **Cost tracking**: Free tier usage monitoring

### CloudWatch Integration
- **System metrics**: CPU, memory, disk (free tier)
- **Custom metrics**: Bot uptime, message count
- **Log aggregation**: Centralized logging
- **Alerts**: Email notifications on failures

---

## Team Collaboration

### Individual Deployments
- **Separate instances** per team member
- **Isolated environments** (no conflicts)
- **Personal credentials** (no sharing)
- **Individual cost tracking**

### Shared Resources
- **Documentation**: Guides and templates
- **Scripts**: Deployment automation
- **Best practices**: Security standards
- **Support**: Team troubleshooting

### Naming Convention
- **Stacks**: `telegram-bot-YOURNAME`
- **Instances**: `TelegramBot-YOURNAME`
- **Environment**: `.env.YOURNAME`

---

## Success Criteria

Your deployment is successful when:
- [ ] Bot responds to trigger messages
- [ ] Admin buttons work in your admin chat
- [ ] Group creation works with all features
- [ ] Service auto-restarts on failure
- [ ] Logs are clean (no errors)
- [ ] AWS costs remain $0
- [ ] Security checklist completed

---

## Support

### Getting Help
1. **Check troubleshooting section** in this README
2. **Review logs**: `sudo journalctl -u telegram-bot -f`
3. **Run diagnostics**: `./cost-monitor.sh` and health checks
4. **Contact team lead** for complex issues

### Contributing
- **Improvements**: Submit pull requests
- **Documentation**: Keep guides updated
- **Security**: Report issues immediately

---

## What You Get

After deployment, you'll have:

- **Enterprise-grade security** with zero exposed credentials
- **100% uptime** with automatic restart and monitoring
- **Complete observability** with logs and health checks
- **Zero cost** using AWS free tier
- **Easy management** with simple commands
- **Hardened infrastructure** following security best practices
- **Team-ready** with isolated, replicable deployments

Your secure, production-ready Telegram bot is ready for deployment.
